import {CashFlowController} from "./controller/CashFlowController";
import {BalanceSheetController} from "./controller/balanceSheetController";
import {getDividendsController} from "./controller/dividendsController";
import {EPSController} from "./controller/epsController";
import {processS3Files, searchFileTickerName} from "./controller/filesController";
import {FiltersController} from "./controller/fitlersController";
import {IncomeStatementController} from "./controller/incomeStatementController";
import {PriceController} from "./controller/priceController";
import {SplitController} from "./controller/splitsController";
import {StatisticsOfTickerController} from "./controller/statisticsOfTickerController";
import {getOutdatedListOfTickers, populateListOfTickers, saveTickersIntoBucket} from "./controller/tickersController";
import {iMessageSQS} from "./repositories/iMessageSQS";
import {DynamoStreamEventType} from "./utils/types/dynamo/DynamoStreamEventType";
import {ListofTickerIdEventType} from "./utils/types/events/ListofTickerIdEventType";
import {TriggerS3EventType} from "./utils/types/s3/TriggerS3EventType";

/*
 *
 */
export const populateStockTickers = async () => {
    try {
        await populateListOfTickers();
        return "Process of populating Stock Tickers has started";
    } catch (error: unknown) {
        console.log(error instanceof Error ? error.message : String(error));
    }
};

/**
 * Retrieves fundamental data for a list of tickers and saves it to S3
 * @param event - Event containing list of ticker IDs to process
 * @returns Processed ticker data or undefined on error
 */
export const getFundamentalDataAndSaveToS3 = async () => {
    try {
        const data = await getOutdatedListOfTickers();
        await saveTickersIntoBucket(data);
        return data;
    } catch (error: any) {
        console.log(error.message);
    }
};

/**
 * Processes files from S3 bucket events or retrieves files for tickers updated today
 * @param event - S3 bucket event trigger (optional)
 * @returns Status message or error message
 */
export const processS3FilesToAddToDynamo = async () => {
    try {
        // Process files for tickers updated today
        await processS3Files();
        return "Processed files for tickers updated today";
    } catch (error: any) {
        console.log(error.message);
        return error.message;
    }
};

/**
 * Processes balance sheet data from DynamoDB streams
 * @param event - DynamoDB Stream event
 * @returns Status message or undefined on error
 */
export const setBalanceSheet = async (event: DynamoStreamEventType) => {
    try {
        const {Records} = event;
        const balanceSheet = new BalanceSheetController();

        for (const record of Records) {
            const {
                dynamodb: {
                    NewImage: {
                        body: {S},
                    },
                },
            } = record;
            const {ticker_internal_id, balance_sheet} = JSON.parse(S);
            const id = parseInt(ticker_internal_id);
            await balanceSheet.parseData(id, balance_sheet);
        }

        return "parse fundamental data";
    } catch (error: any) {
        console.log(error.message);
    }
};

/**
 * Processes income statement data from DynamoDB streams
 * @param event - DynamoDB Stream event
 * @returns Status message or undefined on error
 */
export const setIncomeStatement = async (event: DynamoStreamEventType) => {
    try {
        const {Records} = event;
        const incomeStatement = new IncomeStatementController();

        for (const record of Records) {
            const {
                dynamodb: {
                    NewImage: {
                        body: {S},
                    },
                },
            } = record;
            const {ticker_internal_id, income_statement} = JSON.parse(S);
            const id = parseInt(ticker_internal_id);
            await incomeStatement.parseData(id, income_statement);
        }

        return "parse fundamental data";
    } catch (error: any) {
        console.log(error.message);
    }
};

/**
 * Processes cash flow data from DynamoDB streams
 * @param event - DynamoDB Stream event
 * @returns Status message or undefined on error
 */
export const setCashFlow = async (event: DynamoStreamEventType) => {
    try {
        const {Records} = event;
        const cashFlow = new CashFlowController();

        for (const record of Records) {
            const {
                dynamodb: {
                    NewImage: {
                        body: {S},
                    },
                },
            } = record;
            const {ticker_internal_id, cash_flow} = JSON.parse(S);
            const id = parseInt(ticker_internal_id);
            await cashFlow.parseData(id, cash_flow);
        }

        return "parse fundamental data";
    } catch (error: any) {
        console.log(error.message);
    }
};

/**
 * Updates EPS (Earnings Per Share) data for all tickers
 * @returns Status message
 */
export const setEPS = async () => {
    const eps = new EPSController();
    await eps.getTickers();
    await eps.setEPS();
    return "Set EPS";
};

/**
 * Retrieves dividend information for specified tickers
 * @param event - Event containing list of ticker IDs
 * @returns Status message or undefined on error
 */
export const getDividends = async (event: ListofTickerIdEventType) => {
    try {
        await getDividendsController(event);
        return "Dividends in queue";
    } catch (error: any) {
        console.log(error.message);
    }
};

/**
 * Retrieves and processes stock split information
 * @param event - Event data (currently unused)
 * @returns Status message or undefined on error
 */
export const getSplits = async (event: any) => {
    try {
        const splitController = new SplitController();
        await splitController.fillSplits();
        return "Splits in queue";
    } catch (error: any) {
        console.log(error.message);
    }
};

/**
 * Retrieves current price data for specified tickers
 * @param event - Event containing list of ticker IDs
 * @returns Status message or undefined on error
 */
export const getTicketPrice = async (event: ListofTickerIdEventType) => {
    try {
        const priceController = new PriceController();
        await priceController.getPrices(event);
        return "Ok";
    } catch (error) {
        console.log(error);
    }
};

/**
 * Updates ticket pricing from SQS messages
 * @param event - SQS event containing price messages
 * @returns Status message or undefined on error
 */
export const setTicketPrice = async (event: any) => {
    try {
        const messages: iMessageSQS[] = event?.Records || [];
        if (messages.length > 0) {
            const priceController = new PriceController();
            await priceController.setPrice(messages);
            return "Ok";
        }
        return "No tickers found";
    } catch (error) {
        console.log(error);
    }
};

/**
 * Handles invalid price entries from DynamoDB streams
 * @param event - DynamoDB Stream event
 * @returns Status message
 */
export const setInvalidPrices = async (event: DynamoStreamEventType) => {
    const priceController = new PriceController();
    const {Records} = event;

    for (const record of Records) {
        try {
            const {
                dynamodb: {
                    Keys: {
                        symbol_code: {S: symbol},
                    },
                },
            } = record;
            console.log("symbol code", symbol);
            priceController.setInvalidPrices(symbol);
        } catch (error: any) {
            console.log(error.message);
            continue;
        }
    }
    return "Prices";
};

/**
 * Retrieves statistical data for specified tickers
 * @param event - Event containing list of ticker IDs
 * @returns Status message
 */
export const getStatistics = async (event: ListofTickerIdEventType) => {
    const statisticsController = new StatisticsOfTickerController();
    await statisticsController.getTickers(event);
    await statisticsController.getTickersStatistics();
    return "in queue";
};

/**
 * Searches for file highlights for specified tickers
 * @param event - Event containing list of ticker IDs
 * @returns Status message
 */
export const getFileHighLights = async (event: ListofTickerIdEventType) => {
    const statisticsController = new StatisticsOfTickerController();
    await statisticsController.getTickers(event);
    const tickers = statisticsController.statisticsRepository.tickers;
    await searchFileTickerName(tickers);
    return "Finished";
};

/**
 * Updates statistical data from SQS messages
 * @param event - SQS event containing statistics messages
 * @returns Status message
 */
export const setStatistics = async (event: any) => {
    const messages: iMessageSQS[] = event?.Records || [];
    if (messages.length > 0) {
        const statisticsController = new StatisticsOfTickerController();
        await statisticsController.setTickersStatistics(messages);
        return "in queue";
    }
    return "No tickers found";
};

/**
 * Populates filter data
 * @param event - Event data (currently unused)
 * @returns Status message
 */
export const setFilters = async (event: any) => {
    const filtersController = new FiltersController();
    await filtersController.fillFilters();
    return "in queue";
};

import type {NextApiRequest, NextApiResponse} from "next";
import {getAgenda} from "../../../lib/agenda";
import {JobDetails} from "@/utils/types/agenda/job";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {Job} from "agenda";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "GET") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    const jobs = await getAgenda().jobs({});

    const jobList: JobDetails[] = jobs.map((job: Job) => ({
        _id: job.attrs._id.toString(),
        name: job.attrs.name,
        priority: job.attrs.priority,
        nextRunAt: job.attrs.nextRunAt ?? null,
        type: job.attrs.type === "normal" || job.attrs.type === "single" ? job.attrs.type : "normal",
        data: job.attrs.data || {},
        lastFinishedAt: job.attrs.lastFinishedAt,
        lastRunAt: job.attrs.lastRunAt,
        repeatInterval: job.attrs.repeatInterval || "manual",
        disabled: job.attrs.disabled,
    }));

    res.status(200).json(jobList);
}

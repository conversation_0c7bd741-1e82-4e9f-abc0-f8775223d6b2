import {NextApiRequest, NextApiResponse} from "next";
import {getAgenda} from "@/lib/agenda";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {ObjectId} from "mongodb";

// Get the settings collection
const getAgendaSettingsCollection = () => {
    const agenda = getAgenda();
    const db = agenda._mdb;
    return db.collection("agendaSettings");
};

// Get or create agenda settings
const getOrCreateAgendaSettings = async () => {
    try {
        const settingsId = new ObjectId("00000001c1c580362ac42646"); // Hardcoded ID as in other parts of the codebase
        const settingsCollection = getAgendaSettingsCollection();

        // Check if settings document exists
        let existingSettings = await settingsCollection.findOne({_id: settingsId});
        if (existingSettings) {
            return existingSettings;
        }

        // Create new document with default values
        await settingsCollection.insertOne({
            _id: settingsId,
            agendaStartDateTime: new Date(),
            checkAgendaStatusInterval: 30, // Default value in minutes
        });

        existingSettings = await settingsCollection.findOne({_id: settingsId});
        console.log("Created agendaSettings with default values");
        return existingSettings;
    } catch (error) {
        console.error("Error getting/creating agendaSettings:", error);
        throw error;
    }
};

// Update agenda settings
const updateAgendaSettings = async (settings: any) => {
    try {
        const settingsId = new ObjectId("00000001c1c580362ac42646");
        const settingsCollection = getAgendaSettingsCollection();

        // Get existing settings
        const existingSettings = await settingsCollection.findOne({_id: settingsId});
        if (!existingSettings) {
            return await getOrCreateAgendaSettings();
        }

        // Update settings
        const updatedSettings = {...existingSettings, ...settings};
        await settingsCollection.replaceOne({_id: settingsId}, updatedSettings);

        return updatedSettings;
    } catch (error) {
        console.error("Error updating agendaSettings:", error);
        throw error;
    }
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // Ensure connection to MongoDB
    try {
        await checkIfAgendaIsConnected();
    } catch (error) {
        return res.status(500).json({error: "Failed to connect to database"});
    }

    // Handle GET request
    if (req.method === "GET") {
        try {
            const settings = await getOrCreateAgendaSettings();
            return res.status(200).json(settings);
        } catch (error) {
            console.error("Error fetching agenda settings:", error);
            return res.status(500).json({error: "Failed to fetch agenda settings"});
        }
    }

    // Handle PUT request
    if (req.method === "PUT") {
        try {
            const {checkAgendaStatusInterval} = req.body;

            // Validate input
            if (checkAgendaStatusInterval !== undefined) {
                const interval = Number(checkAgendaStatusInterval);
                if (isNaN(interval) || interval <= 0) {
                    return res.status(400).json({error: "checkAgendaStatusInterval must be a positive number"});
                }

                const updatedSettings = await updateAgendaSettings({checkAgendaStatusInterval: interval});
                return res.status(200).json(updatedSettings);
            } else {
                return res.status(400).json({error: "No valid settings provided"});
            }
        } catch (error) {
            console.error("Error updating agenda settings:", error);
            return res.status(500).json({error: "Failed to update agenda settings"});
        }
    }

    // Handle unsupported methods
    return res.status(405).json({error: "Method not allowed"});
}

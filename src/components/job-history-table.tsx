"use client"

import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import styles from "./job-history-table.module.css"
import axios from "axios"

interface JobHistoryRecord {
  _id: string;
  jobId: string;
  name: string;
  startedAt: string;
  finishedAt?: string;
  duration?: number;
  status: 'started' | 'completed' | 'failed';
  error?: string;
  result?: any;
}

interface JobHistoryResponse {
  history: JobHistoryRecord[];
  pagination: {
    page: number;
    limit: number;
    totalItems: number;
    totalPages: number;
  }
}

interface JobHistoryTableProps {
  jobId: string;
  refreshTrigger?: number;
}

export function JobHistoryTable({ jobId, refreshTrigger = 0 }: JobHistoryTableProps) {
  const [page, setPage] = useState(1)
  const [limit] = useState(10)
  const [loading, setLoading] = useState(true)
  const [history, setHistory] = useState<JobHistoryRecord[]>([])
  const [totalPages, setTotalPages] = useState(1)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchJobHistory = async () => {
      try {
        setLoading(true)
        const response = await axios.get<JobHistoryResponse>(`/api/jobs/history?jobId=${jobId}&page=${page}&limit=${limit}`)
        console.log('job history', JSON.stringify(response.data))
        setHistory(response.data.history)
        setTotalPages(response.data.pagination.totalPages)
        setError(null)
      } catch (err) {
        console.error("Error fetching job history:", err)
        setError("Failed to load job history")
        setHistory([])
      } finally {
        setLoading(false)
      }
    }

    fetchJobHistory()
  }, [jobId, page, limit, refreshTrigger])

  return (
    <div className={styles.container}>
      {loading ? (
        <div className={styles.loading}>Loading job history...</div>
      ) : error ? (
        <div className={styles.error}>{error}</div>
      ) : history.length === 0 ? (
        <div className={styles.empty}>No history records found for this job</div>
      ) : (
        <>
          <div className={styles.tableContainer}>
            <table className={styles.table}>
              <thead className={styles.tableHeader}>
                <tr>
                  <th className={styles.tableHead}>Started At</th>
                  <th className={styles.tableHead}>Finished At</th>
                  <th className={styles.tableHead}>Status</th>
                  <th className={styles.tableHead}>Duration</th>
                </tr>
              </thead>
              <tbody>
                {history.map((record) => (
                  <tr key={record._id} className={styles.tableRow}>
                    <td className={styles.tableCell}>{new Date(record.startedAt).toLocaleString()}</td>
                    <td className={styles.tableCell}>
                      {record.finishedAt ? new Date(record.finishedAt).toLocaleString() : 'In progress'}
                    </td>
                    <td className={styles.tableCell}>
                      <span
                        className={`${styles.badge} ${
                          record.status === "completed"
                            ? styles.completed
                            : record.status === "failed"
                              ? styles.failed
                              : styles.started
                        }`}
                      >
                        {record.status}
                      </span>
                    </td>
                    <td className={styles.tableCell}>
                      {record.duration
                        ? `${(record.duration / 1000).toFixed(2)} seconds`
                        : 'N/A'}
                    </td>                    
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className={styles.pagination}>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className={styles.paginationButton}
            >
              <ChevronLeft className={styles.paginationIcon} />
              Previous
            </Button>
            <div className={styles.paginationInfo}>
              Page {page} of {totalPages || 1}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages || totalPages === 0}
              className={styles.paginationButton}
            >
              Next
              <ChevronRight className={styles.paginationIcon} />
            </Button>
          </div>
        </>
      )}
    </div>
  )
}

import {FilesToCashFlowExecute} from "../execute/filesToCashFlowExecute";
import {LogsOfTickersExecute} from "../execute/logsOfTickersExecute";
import {CashFlowRepository} from "../repositories/implements/CashFlowRepository";
import {LogsController} from "./logsController";

export class CashFlowController {
    async parseData(ticker_internal_id: number, file: any) {
        try {
            const cashFlowRepository = new CashFlowRepository(ticker_internal_id);

            const filesToCashFlow = new FilesToCashFlowExecute(cashFlowRepository, file);

            return await filesToCashFlow.execute();
        } catch (error: any) {
            console.log("Cash Flow", error.message);
            await LogsController.saveError(ticker_internal_id, error.message);
        }
    }
}

import {LatestApi} from "@/entities/consolidated_data/LatestApi";
import {StatisticsOfAPIRepository} from "@/repositories/implements/StatisticsOfAPIRepository";

export class StatisticsOfAPIController {
    async createStatisticsOfAPI(request_date: Date, latest_api: LatestApi = LatestApi.eodhd, api_credit_consumption: number, request_url: string) {
        try {
            const statisticsAPI = {
                request_date,
                latest_api,
                api_credit_consumption,
                request_url,
            };

            const statisticsOfAPIRepository = new StatisticsOfAPIRepository();
            await statisticsOfAPIRepository.create(statisticsAPI);
        } catch (error: any) {
            console.log(error.message);
        }
    }
}

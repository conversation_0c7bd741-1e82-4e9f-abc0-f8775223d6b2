'use client'

import { StatusCard } from "@/components/status-card"
import { JobsOverview } from "@/components/jobs-overview"
import { RecentJobs } from "@/components/recent-jobs"
import styles from "./page.module.css"
import { useEffect, useState, useRef } from "react"
import { StatusCardProps, StatusType } from "@/utils/types/agenda/status"

export default function Dashboard() {
  const [agendaStatus, setAgendaStatus] = useState({
    title: "Agenda Status",
    value: "Checking...",
    description: "Checking connection status",
    status: StatusType.WARNING
  })

  // Reference to store the interval ID for cleanup
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Function to check agenda connection status
  const checkAgendaConnection = async () => {
    try {
      const response = await fetch('/api/agenda/status')
      const data = await response.json()

      setAgendaStatus({
        title: "Agenda Status",
        value: data.connected ? "Running" : "Offline",
        description: data.uptime || "Something is wrong with uptime calculation",
        status: data.status as StatusType
      })
    } catch (error) {
      setAgendaStatus({
        title: "Agenda Status",
        value: "Error",
        description: "Failed to check connection",
        status: StatusType.ERROR
      })
    }
  }

  // Function to fetch the check interval from settings
  const fetchCheckInterval = async (): Promise<number> => {
    try {
      const response = await fetch('/api/agenda/settings')
      const data = await response.json()

      // Return the interval in minutes, default to 30 if not found
      return data.checkAgendaStatusInterval || 30
    } catch (error) {
      console.error("Failed to fetch agenda check interval:", error)
      // Return default value if there's an error
      return 30
    }
  }

  useEffect(() => {
    // Initial check
    checkAgendaConnection()

    // Set up periodic checking based on the configured interval
    const setupPeriodicCheck = async () => {
      // Get the check interval from settings (in minutes)
      const intervalMinutes = await fetchCheckInterval()

      // Convert minutes to milliseconds
      const intervalMs = intervalMinutes * 60 * 1000

      // Clear any existing interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }

      // Set up new interval
      intervalRef.current = setInterval(checkAgendaConnection, intervalMs)

      console.log(`Agenda status check scheduled every ${intervalMinutes} minutes`)
    }

    setupPeriodicCheck()

    // Cleanup function to clear the interval when component unmounts
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>Dashboard</h2>
      </div>
      <div className={styles.statusGrid}>
        <StatusCard
          title="MongoDB Status"
          value="Connected"
          description="Must be on, otherwise has no Agenda"
          status={StatusType.ONLINE}
        />
        <StatusCard
          title={agendaStatus.title}
          value={agendaStatus.value}
          description={agendaStatus.description}
          status={agendaStatus.status}
        />
        {/*<StatusCard title="Active Jobs" value="12" description="2 currently running" status={StatusType.INFO} />
        <StatusCard title="Failed Jobs" value="3" description="Last 24 hours" status={StatusType.ERROR} /> */}
      </div>
       {/*<div className={styles.chartsGrid}>
        <div className={styles.overviewCard}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>Jobs Overview</h3>
            <p className={styles.cardDescription}>Distribution of job statuses in the last 7 days</p>
          </div>
          <div className={styles.cardContent}>
            <JobsOverview />
          </div>
        </div>
        <div className={styles.recentCard}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>Recent Jobs</h3>
            <p className={styles.cardDescription}>Last 5 job executions</p>
          </div>
          <div className={styles.cardContent}>
            <RecentJobs />
          </div>
        </div>
      </div> */}
    </div>
  )
}

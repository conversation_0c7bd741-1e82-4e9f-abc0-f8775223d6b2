'use client'

import Link from "next/link"
import { Play } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import styles from "./page.module.css"
import { useEffect, useState } from "react"
import axios from "axios"
import { JobDetails } from "@/utils/types/agenda/job"

export default function JobsPage() {
  const [jobs, setJobs] = useState<JobDetails[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        const response = await axios.get('/api/jobs/list')
        setJobs(response.data)
      } catch (error) {
        console.error('Error fetching jobs:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchJobs()
  }, [])

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>Jobs</h2>     
      </div>
      <div className={styles.card}>
        <div className={styles.cardHeader}>
          <h3 className={styles.cardTitle}>All Jobs</h3>
          <p className={styles.cardDescription}>View and manage all your scheduled jobs</p>
        </div>
        <div className={styles.cardContent}>
          <div className={styles.tableContainer}>
            {loading ? (
              <p>Loading jobs...</p>
            ) : (
              <table className={styles.table}>
                <thead className={styles.tableHeader}>
                  <tr>
                    <th className={styles.tableHead}>ID</th>
                    <th className={styles.tableHead}>Name</th>
                    <th className={styles.tableHead}>Type</th>
                    <th className={styles.tableHead}>Repeat Interval</th>
                    <th className={styles.tableHead}>Last Run</th>
                    <th className={styles.tableHead}>Next Run</th>
                    <th className={styles.tableHead}>Status</th>
                  </tr>
                </thead>
                <tbody>
                  {jobs.map((job) => (
                    <tr key={job._id} className={styles.tableRow}>
                      <td className={styles.tableCell}>{job._id}</td>
                      <td className={styles.tableCell}>
                        <Link href={`/jobs/${job._id}`} className={styles.jobLink}>
                          {job.name}
                        </Link>
                      </td>
                      <td className={styles.tableCell}>{job.type}</td>
                      <td className={styles.tableCell}>{job.repeatInterval}</td>
                      <td className={styles.tableCell}>{job.lastRunAt ? new Date(job.lastRunAt).toLocaleString() : 'Not run yet'}</td>
                      <td className={styles.tableCell}>{job.nextRunAt ? new Date(job.nextRunAt).toLocaleString() : 'Not scheduled'}</td>
                      <td className={styles.tableCell}>
                        <span className={`${styles.badge} ${job.disabled ? styles.idle : styles.running}`}>
                          {job.disabled ? 'Disabled' : 'Enabled'}
                        </span>
                      </td>                      
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
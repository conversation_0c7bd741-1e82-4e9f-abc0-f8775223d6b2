import {IncomeStatementRepository} from "../repositories/implements/IncomeStatementRepository";

export class FilesToIncomeStatementExecute {
    private incomeStatementRepository: IncomeStatementRepository;
    private responseFile: any;
    private ticker_internal_id: number;

    constructor(incomeStatementRepository: IncomeStatementRepository, responseFile: any) {
        this.incomeStatementRepository = incomeStatementRepository;
        this.responseFile = responseFile;
    }

    async execute() {
        await this.incomeStatementRepository.getSavedYearly();
        await this.incomeStatementRepository.getSavedQuarterly();

        const incomeStatement = this.responseFile || {};

        const yearly = incomeStatement?.yearly || {};
        const quarterly = incomeStatement?.quarterly || {};

        await this.incomeStatementRepository.getYearly(yearly);
        await this.incomeStatementRepository.getQuarterly(quarterly);

        await this.incomeStatementRepository.saveYearly();
        await this.incomeStatementRepository.saveQuarterly();

        // Get balance sheet data arrays
        const incomeStatementYearly = this.incomeStatementRepository.incomeStatementYearly || [];
        const incomeStatementQuarterly = this.incomeStatementRepository.incomeStatementQuarterly || [];

        // Calculate quantities
        const quantity_of_income_statement_year = incomeStatementYearly.length;
        const quantity_of_income_statement_quarter = incomeStatementQuarterly.length;

        // Calculate date ranges for yearly data
        let start_of_income_statement_year = "";
        let end_of_income_statement_year = "";

        if (incomeStatementYearly.length > 0) {
            // First record date (earliest)
            start_of_income_statement_year = incomeStatementYearly[incomeStatementYearly.length - 1]?.document_date || "";
            // Last record date (latest)
            end_of_income_statement_year = incomeStatementYearly[0]?.document_date || "";
        }

        // Calculate date ranges for quarterly data
        let start_of_income_statement_quarter = "";
        let end_of_income_statement_quarter = "";

        if (incomeStatementQuarterly.length > 0) {
            // First record date (earliest)
            start_of_income_statement_quarter = incomeStatementQuarterly[incomeStatementQuarterly.length - 1]?.document_date || "";
            // Last record date (latest)
            end_of_income_statement_quarter = incomeStatementQuarterly[0]?.document_date || "";
        }

        return {
            quantity_of_income_statement_year,
            quantity_of_income_statement_quarter,
            start_of_income_statement_year,
            end_of_income_statement_year,
            start_of_income_statement_quarter,
            end_of_income_statement_quarter,
        };
    }
}

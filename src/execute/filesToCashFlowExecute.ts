import {CashFlowRepository} from "../repositories/implements/CashFlowRepository";

export class FilesToCashFlowExecute {
    private cashFlowRepository: CashFlowRepository;
    private responseFile: any;

    constructor(cashFlowRepository: CashFlowRepository, responseFile: any) {
        this.cashFlowRepository = cashFlowRepository;
        this.responseFile = responseFile;
    }

    async execute() {
        await this.cashFlowRepository.getSavedYearly();
        await this.cashFlowRepository.getSavedQuarterly();

        const cashFlow = this.responseFile || {};

        const yearly = cashFlow?.yearly || {};
        const quarterly = cashFlow?.quarterly || {};

        await this.cashFlowRepository.getYearly(yearly);
        await this.cashFlowRepository.getQuarterly(quarterly);

        await this.cashFlowRepository.saveYearly();
        await this.cashFlowRepository.saveQuarterly();

        // Get balance sheet data arrays
        const cashFlowYearly = this.cashFlowRepository.cashFlowYearly || [];
        const cashFlowQuarterly = this.cashFlowRepository.cashFlowQuarterly || [];

        // Calculate quantities
        const quantity_of_cash_flow_year = cashFlowYearly.length;
        const quantity_of_cash_flow_quarter = cashFlowQuarterly.length;

        // Calculate date ranges for yearly data
        let start_of_cash_flow_year = "";
        let end_of_cash_flow_year = "";

        if (cashFlowYearly.length > 0) {
            // First record date (earliest)
            start_of_cash_flow_year = cashFlowYearly[cashFlowYearly.length - 1]?.document_date || "";
            // Last record date (latest)
            end_of_cash_flow_year = cashFlowYearly[0]?.document_date || "";
        }

        // Calculate date ranges for quarterly data
        let start_of_cash_flow_quarter = "";
        let end_of_cash_flow_quarter = "";

        if (cashFlowQuarterly.length > 0) {
            // First record date (earliest)
            start_of_cash_flow_quarter = cashFlowQuarterly[cashFlowQuarterly.length - 1]?.document_date || "";
            // Last record date (latest)
            end_of_cash_flow_quarter = cashFlowQuarterly[0]?.document_date || "";
        }

        return {
            quantity_of_cash_flow_year,
            quantity_of_cash_flow_quarter,
            start_of_cash_flow_year,
            end_of_cash_flow_year,
            start_of_cash_flow_quarter,
            end_of_cash_flow_quarter,
        };
    }
}

import {Op} from "sequelize";

import {ListOfTickers, tickers, TickersInput} from "../../entities/consolidated_data/ListOfTickers";
import {IListOfTickersRepository} from "../IListOfTickersRepository";
import {LogsController} from "@/controller/logsController";

const api_limit = process.env.API_EOD_LIMIT ? parseInt(process.env.API_EOD_LIMIT) : 6;

type ListOfTickersWithoutError = {
    rows: ListOfTickers[] | [];
    count: number | 0;
    current_page: number;
};

export class ListOfTickersRepository implements IListOfTickersRepository {
    async index(listOfTickersId?: number[]): Promise<ListOfTickers[]> {
        const tickers_count = await ListOfTickers.count({
            where: {
                is_enable: 1,
            },
        });

        const mod = tickers_count % api_limit || 0;
        const limit = Math.trunc(tickers_count / api_limit) + mod;

        const params: any = {
            order: [["id", "ASC"]],
            where: {
                is_enable: 1,
            },
        };

        const date = new Date();
        const minutes = date.getMinutes();

        const page = minutes < 10 ? 0 : Math.floor(minutes / 10);

        if (listOfTickersId && listOfTickersId.length > 0) {
            params.where.id = {
                [Op.in]: listOfTickersId,
            };
        } else {
            params.offset = page * limit;
            params.limit = limit;
        }
        console.log("page", page, "limit", limit, "offset", page * limit, "count", tickers_count, "params", params);

        const res = await tickers.findAll(params);

        console.log("tamanho", res.length);

        return res;
    }

    async whithoutError(page: number, limit: number = 88, listOfTickersId?: number[]): Promise<ListOfTickersWithoutError> {
        const params: any = {
            attributes: ["primary_ticker_eodhd", "id"],
            order: [["id", "ASC"]],
        };

        if (listOfTickersId && listOfTickersId.length > 0) {
            params.where = {
                id: {
                    [Op.in]: listOfTickersId,
                },
                is_enable: 1,
            };
        } else {
            params.where = {
                is_enable: 1,
            };
            params.limit = limit;
            params.offset = page * limit;
        }

        const tickers = await ListOfTickers.findAndCountAll(params);

        return {...tickers, current_page: page};
    }

    async bulkCreateOrUpdate(tickersData: Partial<ListOfTickers>[]): Promise<ListOfTickers[]> {
        console.log(`Create or update ${tickersData.length} tickers`);
        const startTime = Date.now();
        const results: ListOfTickers[] = [];
        const lastTicker = await ListOfTickers.findOne({
            order: [["id", "DESC"]],
        });
        let nextId = lastTicker && lastTicker.id ? lastTicker.id + 1 : 1;
        console.log("This is the nextId: ", nextId);
        for (const tickerData of tickersData) {
            // Try to find existing ticker by primary_ticker_eodhd
            let existingTicker = await ListOfTickers.findOne({
                where: {primary_ticker_eodhd: tickerData.primary_ticker_eodhd},
            });

            // For German stocks, if not found with XETR, try with BE (Berlin)
            if (!existingTicker && tickerData.primary_ticker_eodhd && tickerData.primary_ticker_eodhd.endsWith(".XETRA")) {
                const berlinTicker = tickerData.primary_ticker_eodhd.replace(".XETRA", ".BE");
                existingTicker = await ListOfTickers.findOne({
                    where: {primary_ticker_eodhd: berlinTicker},
                });
            }

            if (existingTicker) {
                // Update existing ticker
                Object.assign(existingTicker, tickerData);
                existingTicker.updated_at = new Date();
                await existingTicker.save();
                results.push(existingTicker);
                const logsController = new LogsController();
                if (existingTicker.id) {
                    logsController.tickerUpdatedEODHD(existingTicker.id, "Ticker updated");
                }
            } else {
                tickerData.id = nextId;
                tickerData.created_at = new Date();
                tickerData.updated_at = new Date();
                tickerData.is_enable = 1;
                const newTicker = await ListOfTickers.create(tickerData as TickersInput);
                results.push(newTicker);
                nextId++;
                const logsController = new LogsController();
                logsController.tickerUpdatedEODHD(tickerData.id, "Ticker created");
            }
        }
        console.log(`Created or updated ${results.length} tickers in ${Date.now() - startTime}ms`);
        return results;
    }

    async getOutdatedTickers(daysThreshold: number = 7): Promise<ListOfTickers[]> {
        console.log(`Retrieving tickers not updated in the last ${daysThreshold} days`);
        const startTime = Date.now();

        const thresholdDate = new Date();
        thresholdDate.setDate(thresholdDate.getDate() - daysThreshold);
        console.log(`Threshold date: ${thresholdDate.toISOString()}`);

        const params: any = {
            attributes: ["primary_ticker_eodhd", "id"],
            order: [["id", "ASC"]],
            where: {
                is_enable: 1,
                [Op.or]: [{fundamental_data_last_updated: {[Op.lt]: thresholdDate}}, {fundamental_data_last_updated: null}],
            },
        };

        const outdatedTickers = await tickers.findAll(params);
        const executionTime = Date.now() - startTime;

        console.log(`Found ${outdatedTickers.length} outdated tickers in ${executionTime}ms`);
        return outdatedTickers;
    }

    async markAsUpdated(tickerIds: number[]): Promise<number> {
        if (!tickerIds.length) return 0;

        const result = await tickers.update(
            {
                fundamental_data_last_updated: new Date(),
            },
            {
                where: {
                    id: {
                        [Op.in]: tickerIds,
                    },
                },
            },
        );

        return result[0]; // Return number of updated rows
    }
}

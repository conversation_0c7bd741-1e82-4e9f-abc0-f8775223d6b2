import {LatestApi} from "@/entities/consolidated_data/LatestApi";
import {StatisticsOfAPI} from "../../entities/consolidated_data/StatisticsOfAPI";

export class StatisticsOfAPIRepository {
    async create(_statisticsAPI: Partial<StatisticsOfAPI>) {
        try {
            const lastStatistics = await StatisticsOfAPI.findOne({
                order: [["id", "DESC"]],
            });
            let nextId = lastStatistics && lastStatistics.id ? lastStatistics.id + 1 : 1;
            _statisticsAPI.id = nextId;
            await StatisticsOfAPI.create(_statisticsAPI);
        } catch (error: any) {
            console.log(error.message);
        }
    }
}

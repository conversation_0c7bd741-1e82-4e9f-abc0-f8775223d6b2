import {BalanceSheet} from "../../entities/consolidated_data/BalanceSheet";
import {Document_type_year_or_quarter} from "../../entities/consolidated_data/DocumentTypeYearorQuarterly";
import {iBalanceSheetDAO} from "../iBalanceSheetDAO";
import {BalanceSheetDAO} from "./BalanceSheetDAO";
import _, {snakeCase} from "lodash";

export class BalanceSheetRepository {
    saved_yearly: string[];
    saved_quarterly: string[];
    balanceSheetYearly: BalanceSheetDAO[];
    balanceSheetQuarterly: BalanceSheetDAO[];
    ticker_internal_id: number;
    keys_yearly_file: string[];
    keys_quarterly_file: string[];

    constructor(ticker_internal_id: number) {
        this.ticker_internal_id = ticker_internal_id;
    }

    async getSavedYearly(): Promise<void> {
        console.log("aqui", this.ticker_internal_id);

        const saved_yearly = await BalanceSheet.findAll({
            where: {
                ticker_internal_id: this.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.y,
            },
            attributes: ["document_date"],
        });

        const saved: string[] = [];

        for (let i = 0; i < saved_yearly.length; i++) {
            saved.push(saved_yearly[i].document_date);
        }

        this.saved_yearly = saved;
    }

    async getSavedQuarterly(): Promise<void> {
        const saved_quarterly = await BalanceSheet.findAll({
            where: {
                ticker_internal_id: this.ticker_internal_id,
                document_type_year_or_quarter: Document_type_year_or_quarter.q,
            },
            attributes: ["document_date"],
        });

        const saved: string[] = [];

        for (let i = 0; i < saved_quarterly.length; i++) {
            saved.push(saved_quarterly[i].document_date);
        }

        this.saved_quarterly = saved;
    }

    async getYearly(balance_sheet_file_yearly: any) {
        try {
            const ticker_internal_id = this.ticker_internal_id;

            const data: BalanceSheetDAO[] = [];

            const keys = Object.keys(balance_sheet_file_yearly);

            this.keys_yearly_file = keys;

            for (let i = 0; i < keys.length; i++) {
                const current_key = keys[i];

                const balanceSheet = balance_sheet_file_yearly[current_key];

                const snakeCasedObject = Object.keys(balanceSheet).reduce(
                    (result, key) => ({
                        ...result,
                        ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.y,
                        date: current_key,
                        [snakeCase(key)]: balanceSheet[key],
                    }),
                    {
                        ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.y,
                        date: current_key,
                    },
                );

                const balance = new BalanceSheetDAO({
                    ...snakeCasedObject,
                });

                data.push(balance);
            }

            this.balanceSheetYearly = data;
        } catch (error: any) {
            throw Error("Error when try to get BalanceSheet Yearly " + error.message);
        }
    }

    async getQuarterly(balance_sheet_file_quarterly: any) {
        try {
            const ticker_internal_id = this.ticker_internal_id;

            const data: BalanceSheetDAO[] = [];

            const keys: string[] = Object.keys(balance_sheet_file_quarterly);

            this.keys_quarterly_file = keys;

            for (let i = 0; i < keys.length; i++) {
                const current_key = keys[i];

                const balanceSheet = balance_sheet_file_quarterly[current_key];

                const snakeCasedObject: iBalanceSheetDAO = Object.keys(balanceSheet).reduce(
                    (result, key) => ({
                        ...result,
                        date: current_key,
                        ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.q,
                        [snakeCase(key)]: balanceSheet[key],
                    }),
                    {
                        date: current_key,
                        ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.q,
                    },
                );

                const balance = new BalanceSheetDAO({
                    ...snakeCasedObject,
                });

                data.push(balance);
            }

            this.balanceSheetQuarterly = data;
        } catch (error: any) {
            throw Error("Error when try to get BalanceSheet Quarterly " + error.message);
        }
    }

    async saveYearly() {
        try {
            let toSave: BalanceSheetDAO[] = [];

            if (this.saved_yearly.length === 0 && this.balanceSheetYearly.length > 0) {
                toSave = this.balanceSheetYearly;
                toSave[0].document_current = 1;
            } else {
                const delta = _.difference(this.keys_yearly_file, this.saved_yearly);

                const balance = await BalanceSheet.findOne({
                    where: {
                        document_current: 1,
                        ticker_internal_id: this.ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.y,
                    },
                });

                let bigger_date_file = this.keys_yearly_file[0];

                for (let i = 0; i < this.balanceSheetYearly.length; i++) {
                    if (delta?.includes(this.balanceSheetYearly[i].document_date)) {
                        if (this.balanceSheetYearly[i].document_date === bigger_date_file) {
                            this.balanceSheetYearly[i].document_current = 1;
                        }

                        toSave.push(this.balanceSheetYearly[i]);
                    }
                }

                if (balance && balance.document_date < bigger_date_file) {
                    balance.document_current = 0;
                    await balance.save();
                }
            }

            if (toSave.length > 0) {
                await BalanceSheet.bulkCreate(toSave); //cast to interface BalanceSheet
                console.log("BalanceSheet yearly saved");
            }
        } catch (error: any) {
            throw "Error when save BalanceSheet yearly " + error.message;
        }
    }

    async saveQuarterly() {
        try {
            let toSave: BalanceSheetDAO[] = [];

            if (this.saved_quarterly.length === 0) {
                toSave = this.balanceSheetQuarterly;
                toSave[0].document_current = 1;
            } else {
                const delta = _.difference(this.keys_quarterly_file, this.saved_quarterly);

                const balance = await BalanceSheet.findOne({
                    where: {
                        document_current: 1,
                        ticker_internal_id: this.ticker_internal_id,
                        document_type_year_or_quarter: Document_type_year_or_quarter.q,
                    },
                });

                let bigger_date_file = this.keys_quarterly_file[0];

                for (let i = 0; i < this.balanceSheetQuarterly.length; i++) {
                    if (delta?.includes(this.balanceSheetQuarterly[i].document_date)) {
                        if (this.balanceSheetQuarterly[i].document_date === bigger_date_file) {
                            this.balanceSheetQuarterly[i].document_current = 1;
                        }

                        toSave.push(this.balanceSheetQuarterly[i]);
                    }
                }

                if (balance && balance.document_date < bigger_date_file) {
                    balance.document_current = 0;
                    await balance.save();
                }
            }

            if (toSave.length > 0) {
                await BalanceSheet.bulkCreate(toSave); //cast to interface BalanceSheet
                console.log("BalanceSheet quarterly saved");
            }
        } catch (error: any) {
            throw "Error when save BalanceSheet quarterly " + error.message;
        }
    }
}

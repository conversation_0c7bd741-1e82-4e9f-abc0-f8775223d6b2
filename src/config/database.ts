import {Sequelize, Options} from "sequelize";
import dotenv from "dotenv";

dotenv.config();

const {DB_USER, DB_NAME, DB_HOST, DB_PASS, DB_PORT} = process.env;

// Validate database configuration
if (!DB_USER || !DB_NAME || !DB_HOST || !DB_PASS) {
    throw new Error("Database configuration missing. Please check your environment variables: " + "DB_USER, DB_NAME, DB_HOST, DB_PASS are required");
}

// Create a singleton instance
let sequelizeInstance: Sequelize | null = null;

// Configure Sequelize options based on environment
const getSequelizeOptions = (): Options => {
    const baseOptions: Options = {
        dialect: "mysql",
        host: DB_HOST || "",
        port: parseInt(DB_PORT || "3306"),
        define: {
            timestamps: false,
        },
        logging: false,
    };

    return {
        ...baseOptions,
        pool: {
            max: 5,
            min: 0,
            idle: 1000,
            acquire: 1000,
            evict: 500000,
        },
    };
};

export const getSequelize = (): Sequelize => {
    if (!sequelizeInstance) {
        sequelizeInstance = new Sequelize(DB_NAME || "", DB_USER || "", DB_PASS || "", getSequelizeOptions());
    }
    return sequelizeInstance;
};

// Initialize database connection with retry logic
export const initDatabase = async (retries = 3): Promise<boolean> => {
    let attempt = 0;

    while (attempt < retries) {
        try {
            const sequelize = getSequelize();
            await sequelize.authenticate();
            console.log("Database connection has been established successfully.");
            return true;
        } catch (err) {
            attempt++;
            console.error(`Unable to connect to the database (attempt ${attempt}/${retries}):`, err);

            if (attempt >= retries) {
                console.error("Max retries reached. Database connection failed.");
                return false;
            }

            // Wait before retrying
            await new Promise((resolve) => setTimeout(resolve, 1000));
        }
    }

    return false;
};

// Close database connection
export const closeDatabase = async () => {
    if (sequelizeInstance) {
        await sequelizeInstance.close();
        sequelizeInstance = null;
        console.log("Database connection closed.");
    }
};

// Export the sequelize getter for direct access when needed
export const sequelize = getSequelize();

export default {
    sequelize,
    initDatabase,
    closeDatabase,
    getSequelize,
};

import {processS3Files} from "../../src/controller/filesController";
import {ListOfTickers} from "../../src/entities/consolidated_data/ListOfTickers";
import {FundamentalDataRepository} from "../../src/repositories/implements/FundamentaDataRepository";
import {DynamoController} from "../../src/controller/dynamoController";
import {Op} from "sequelize";

// Mock dependencies
jest.mock("../../src/entities/consolidated_data/ListOfTickers", () => {
    return {
        ListOfTickers: {
            findAll: jest.fn(),
        },
    };
});

jest.mock("../../src/repositories/implements/FundamentaDataRepository", () => {
    return {
        FundamentalDataRepository: jest.fn().mockImplementation(() => {
            return {
                get: jest.fn().mockResolvedValue({ticker_internal_id: "123", Financials: {}}),
            };
        }),
    };
});

jest.mock("../../src/controller/dynamoController", () => {
    return {
        DynamoController: jest.fn().mockImplementation(() => {
            return {
                saveFundamentalData: jest.fn().mockResolvedValue({}),
            };
        }),
    };
});

describe("filesController", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("findFiles", () => {
        it("should retrieve tickers updated today when no records provided", async () => {
            // Arrange
            const mockTickers = [
                {id: 1, symbol_code: "AAPL", primary_ticker_eodhd: "AAPL.US"},
                {id: 2, symbol_code: "MSFT", primary_ticker_eodhd: "MSFT.US"},
            ];

            (ListOfTickers.findAll as jest.Mock).mockResolvedValue(mockTickers);

            // Act
            await processS3Files();

            // Assert
            // Verify ListOfTickers.findAll was called with the correct parameters
            expect(ListOfTickers.findAll).toHaveBeenCalledTimes(1);

            const findAllCall = (ListOfTickers.findAll as jest.Mock).mock.calls[0][0];
            expect(findAllCall.where.is_enable).toBe(1);
            expect(findAllCall.where.fundamental_data_last_updated[Op.gte]).toBeDefined();

            // Verify that the mocked constructor was called (instances were created)
            expect(FundamentalDataRepository).toHaveBeenCalledTimes(1);
            expect(DynamoController).toHaveBeenCalledTimes(1);

            // Get the mock instances that were created inside processS3Files
            const fundamentalDataMockInstance = (FundamentalDataRepository as jest.Mock).mock.results[0].value;
            const dynamoControllerMockInstance = (DynamoController as jest.Mock).mock.results[0].value;

            // Verify fundamentalData.get and dynamoController.saveFundamentalData were called for each ticker
            expect(fundamentalDataMockInstance.get).toHaveBeenCalledTimes(2);
            expect(fundamentalDataMockInstance.get).toHaveBeenCalledWith("fundamentals_AAPL.US_eod.json");
            expect(fundamentalDataMockInstance.get).toHaveBeenCalledWith("fundamentals_MSFT.US_eod.json");

            expect(dynamoControllerMockInstance.saveFundamentalData).toHaveBeenCalledTimes(2);
        });
    });
});
